"use client";

import { useAuth } from "@/lib/hooks/useAuth";
import { useSuperAdmin } from "@/lib/hooks/useSuperAdmin";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useState, useEffect } from "react";

export default function DebugAdminPage() {
  const { user, loading: authLoading } = useAuth();
  const { isAdmin, adminData, loading: adminLoading, error } = useSuperAdmin(user?.uid);
  const [rawAdminData, setRawAdminData] = useState<any>(null);
  const [rawLoading, setRawLoading] = useState(false);
  const [cookies, setCookies] = useState<string>("");

  const fetchRawAdminData = async () => {
    if (!user?.uid) return;
    
    setRawLoading(true);
    try {
      const adminDoc = await getDoc(doc(db, "superadmins", user.uid));
      if (adminDoc.exists()) {
        setRawAdminData(adminDoc.data());
      } else {
        setRawAdminData(null);
      }
    } catch (error) {
      console.error("Error fetching raw admin data:", error);
    } finally {
      setRawLoading(false);
    }
  };

  useEffect(() => {
    if (user?.uid) {
      fetchRawAdminData();
    }
    // Cookie'leri al
    if (typeof window !== 'undefined') {
      setCookies(document.cookie);
    }
  }, [user?.uid]);

  return (
    <div style={{ padding: "20px", fontFamily: "monospace" }}>
      <h1>Admin Debug Sayfası</h1>
      
      <div style={{ marginBottom: "20px", padding: "10px", border: "1px solid #ccc" }}>
        <h2>Auth Durumu</h2>
        <p><strong>Loading:</strong> {authLoading ? "true" : "false"}</p>
        <p><strong>User UID:</strong> {user?.uid || "null"}</p>
        <p><strong>User Email:</strong> {user?.email || "null"}</p>
      </div>

      <div style={{ marginBottom: "20px", padding: "10px", border: "1px solid #ccc" }}>
        <h2>useSuperAdmin Hook Durumu</h2>
        <p><strong>Loading:</strong> {adminLoading ? "true" : "false"}</p>
        <p><strong>isAdmin:</strong> {isAdmin ? "true" : "false"}</p>
        <p><strong>Error:</strong> {error ? error.message : "null"}</p>
        <p><strong>Admin Data:</strong></p>
        <pre style={{ backgroundColor: "#f5f5f5", padding: "10px" }}>
          {adminData ? JSON.stringify(adminData, null, 2) : "null"}
        </pre>
      </div>

      <div style={{ marginBottom: "20px", padding: "10px", border: "1px solid #ccc" }}>
        <h2>Raw Firebase Data</h2>
        <button onClick={fetchRawAdminData} disabled={rawLoading}>
          {rawLoading ? "Yükleniyor..." : "Yeniden Yükle"}
        </button>
        <p><strong>Raw Admin Data:</strong></p>
        <pre style={{ backgroundColor: "#f5f5f5", padding: "10px" }}>
          {rawAdminData ? JSON.stringify(rawAdminData, null, 2) : "null"}
        </pre>
      </div>

      <div style={{ marginBottom: "20px", padding: "10px", border: "1px solid #ccc" }}>
        <h2>Cookie Durumu</h2>
        <p><strong>Cookies:</strong></p>
        <pre style={{ backgroundColor: "#f5f5f5", padding: "10px" }}>
          {cookies || "Hiç cookie yok"}
        </pre>
      </div>

      <div style={{ marginBottom: "20px", padding: "10px", border: "1px solid #ccc" }}>
        <h2>Test Linkleri</h2>
        <p><a href="/admin" target="_blank">Admin Sayfası</a></p>
        <p><a href="/admin/register" target="_blank">Admin Register</a></p>
        <p><a href="/make-admin" target="_blank">Make Admin</a></p>
      </div>
    </div>
  );
}
