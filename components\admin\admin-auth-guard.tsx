"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/hooks/useAuth";
import { useSuperAdmin } from "@/lib/hooks/useSuperAdmin";
import { Loader2 } from "lucide-react";
import type { AdminPermission } from "@/lib/types/firebase";

interface AdminAuthGuardProps {
  children: React.ReactNode;
}

export function AdminAuthGuard({ children }: AdminAuthGuardProps) {
  const { user, loading: authLoading } = useAuth();
  const { isAdmin, loading: adminLoading, hasPermission } = useSuperAdmin(user?.uid);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!authLoading && !adminLoading) {
      if (!user) {
        router.replace("/auth/sign-in?callbackUrl=/admin");
        return;
      }
      // <PERSON><PERSON> bazlı yetki kontrolü
      const requiredPermission = getRequiredPermission(pathname);
      if (requiredPermission && !hasPermission(requiredPermission)) {
        router.replace("/admin"); // veya error sayfası
        return;
      }
    }
  }, [user, isAdmin, authLoading, adminLoading, router, pathname, hasPermission]);

  if (authLoading || adminLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-sm text-muted-foreground">Yetki kontrol ediliyor...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    router.replace("/");
    return;
  }


  return <>{children}</>;
}

// Sayfa bazlı gerekli yetkileri belirle
function getRequiredPermission(pathname: string) {
  const permissionMap: Record<string, AdminPermission> = {
    "/admin/users": "users.view",
    "/admin/users/[id]": "users.manage",
    "/admin/groups": "groups.view",
    "/admin/groups/[id]": "groups.manage",
    "/admin/reports": "reports.view",
    "/admin/settings": "settings.manage",
  };

  // Dinamik route'ları kontrol et
  const route = Object.keys(permissionMap).find(route => {
    const pattern = new RegExp(
      "^" + route.replace(/\[.*?\]/g, "[^/]+") + "/?$"
    );
    return pattern.test(pathname);
  });

  return route ? permissionMap[route] : null;
} 